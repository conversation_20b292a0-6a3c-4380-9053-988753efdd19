{"name": "chatgpt", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "test": "jest --watchAll"}, "jest": {"preset": "jest-expo"}, "dependencies": {"@clerk/clerk-expo": "^1.0.0", "@expo/vector-icons": "^14.1.0", "@gorhom/bottom-sheet": "^4.6.1", "@likashefqet/react-native-image-zoom": "^3.0.0", "@react-navigation/drawer": "^7.3.9", "@react-navigation/native": "^7.1.6", "@shopify/flash-list": "1.7.6", "expo": "^53.0.0", "expo-blur": "~14.1.5", "expo-build-properties": "~0.14.8", "expo-clipboard": "~7.1.5", "expo-dev-client": "~5.2.4", "expo-document-picker": "~13.1.6", "expo-font": "~13.3.2", "expo-image-picker": "~16.1.4", "expo-linear-gradient": "~14.1.5", "expo-linking": "~7.1.7", "expo-media-library": "~17.1.7", "expo-router": "~5.1.4", "expo-secure-store": "~14.2.3", "expo-sharing": "~13.1.5", "expo-splash-screen": "~0.30.10", "expo-sqlite": "~15.2.14", "expo-status-bar": "~2.2.3", "expo-system-ui": "~5.0.10", "expo-web-browser": "~14.2.0", "openai": "^4.38.2", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.76.3", "react-native-gesture-handler": "~2.24.0", "react-native-ios-context-menu": "^2.5.1", "react-native-ios-utilities": "^4.4.5", "react-native-mmkv": "^2.12.2", "react-native-openai": "^0.6.1", "react-native-purchases": "^7.26.2", "react-native-reanimated": "~3.17.4", "react-native-redash": "^18.1.3", "react-native-root-siblings": "^4.1.1", "react-native-root-toast": "^3.5.1", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-shimmer-placeholder": "^2.0.9", "react-native-web": "^0.20.0", "zeego": "^1.10.0"}, "devDependencies": {"@babel/core": "^7.26.0", "@types/react": "~19.0.10", "jest": "^29.2.1", "jest-expo": "~53.0.9", "react-test-renderer": "18.2.0", "typescript": "~5.8.3"}, "private": true}