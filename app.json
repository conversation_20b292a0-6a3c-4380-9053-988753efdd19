{"expo": {"name": "Chatgpt", "slug": "chatgpt", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "chatgpt", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.supersimon.chatgpt"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "com.supersimon.chatgpt", "permissions": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.ACCESS_MEDIA_LOCATION"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-secure-store", ["expo-build-properties", {"android": {"minSdkVersion": 24, "compileSdkVersion": 35, "targetSdkVersion": 34}, "ios": {"deploymentTarget": "15.1"}}], ["expo-media-library", {"photosPermission": "Allow $(PRODUCT_NAME) to access your photos.", "savePhotosPermission": "Allow $(PRODUCT_NAME) to save photos.", "isAccessMediaLocationEnabled": true}]], "experiments": {"typedRoutes": true}, "extra": {"router": {}, "eas": {"projectId": "e3b93ac0-354d-4a84-a9c0-c736f2b967da"}}}}