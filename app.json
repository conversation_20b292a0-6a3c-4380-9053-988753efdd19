{"expo": {"name": "Chatgpt", "slug": "chatgpt", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "chatgpt", "userInterfaceStyle": "automatic", "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "bundleIdentifier": "com.supersimon.chatgpt", "deploymentTarget": "15.1"}, "android": {"compileSdkVersion": 35, "targetSdkVersion": 35, "buildToolsVersion": "35.0.0", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "com.supersimon.chatgpt"}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-secure-store", ["expo-build-properties", {"android": {"minSdkVersion": 24, "compileSdkVersion": 33, "targetSdkVersion": 33, "buildToolsVersion": "33.0.0", "kotlinVersion": "1.6.20"}, "ios": {"deploymentTarget": "15.1"}}], ["expo-media-library", {"photosPermission": "Allow $(PRODUCT_NAME) to access your photos.", "savePhotosPermission": "Allow $(PRODUCT_NAME) to save photos.", "isAccessMediaLocationEnabled": true}]], "experiments": {"typedRoutes": true}}}