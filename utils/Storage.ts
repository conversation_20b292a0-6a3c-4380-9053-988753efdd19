import { MMKV } from "react-native-mmkv";

let _storage: MMKV | null = null;
let _keyStorage: MMKV | null = null;
let _chatStorage: MMKV | null = null;

// Lazy initialization to avoid JSI issues in development
export const getStorage = (): MMKV => {
  if (!_storage) {
    try {
      _storage = new MMKV({
        id: "gptversion",
      });
    } catch (error) {
      console.warn("Failed to initialize MMKV storage:", error);
      // Fallback to a mock implementation for development
      _storage = createMockMMKV();
    }
  }
  return _storage;
};

export const getKeyStorage = (): MMKV => {
  if (!_keyStorage) {
    try {
      _keyStorage = new MMKV({
        id: "openaikey",
      });
    } catch (error) {
      console.warn("Failed to initialize MMKV keyStorage:", error);
      // Fallback to a mock implementation for development
      _keyStorage = createMockMMKV();
    }
  }
  return _keyStorage;
};

export const getChatStorage = (): MMKV => {
  if (!_chatStorage) {
    try {
      _chatStorage = new MMKV({
        id: "chats",
      });
    } catch (error) {
      console.warn("Failed to initialize MMKV chatStorage:", error);
      // Fallback to a mock implementation for development
      _chatStorage = createMockMMKV();
    }
  }
  return _chatStorage;
};

// Mock MMKV implementation for development/debugging
const createMockMMKV = (): MMKV => {
  const mockStorage = new Map<string, any>();

  return {
    set: (key: string, value: any) => {
      mockStorage.set(key, value);
    },
    getString: (key: string) => {
      return mockStorage.get(key) || undefined;
    },
    getNumber: (key: string) => {
      return mockStorage.get(key) || undefined;
    },
    getBoolean: (key: string) => {
      return mockStorage.get(key) || undefined;
    },
    getBuffer: (key: string) => {
      return mockStorage.get(key) || undefined;
    },
    contains: (key: string) => {
      return mockStorage.has(key);
    },
    delete: (key: string) => {
      mockStorage.delete(key);
    },
    getAllKeys: () => {
      return Array.from(mockStorage.keys());
    },
    clearAll: () => {
      mockStorage.clear();
    },
    addOnValueChangedListener: () => {
      return () => {}; // Return empty cleanup function
    },
    removeOnValueChangedListener: () => {},
  } as MMKV;
};

// Create lazy proxy objects that initialize MMKV only when accessed
export const storage = new Proxy({} as MMKV, {
  get(target, prop) {
    const instance = getStorage();
    return instance[prop as keyof MMKV];
  },
});

export const keyStorage = new Proxy({} as MMKV, {
  get(target, prop) {
    const instance = getKeyStorage();
    return instance[prop as keyof MMKV];
  },
});

export const chatStorage = new Proxy({} as MMKV, {
  get(target, prop) {
    const instance = getChatStorage();
    return instance[prop as keyof MMKV];
  },
});
