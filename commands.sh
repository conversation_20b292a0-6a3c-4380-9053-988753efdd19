npx create-expo-app chatgpt -t tabs
npx expo install expo-dev-client react-native-reanimated react-native-gesture-handler

npm i react-native-redash

npm install @clerk/clerk-expo
npx expo install expo-secure-store

npx expo install @react-navigation/drawer

npm install zeego react-native-ios-context-menu react-native-ios-utilities
npx expo install react-native-mmkv

npx expo install expo-blur
npx expo install expo-document-picker
npx expo install expo-image-picker

npm i react-native-openai
npx expo install expo-build-properties 
npx expo install @shopify/flash-list


# For new app builds
npx expo prebuild