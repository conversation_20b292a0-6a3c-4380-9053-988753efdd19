# React Native ChatGPT Clone with Clerk & RevenueCat

This is a React Native ChatGPT clone using [Clerk](https://go.clerk.com/wvMHe8T) for user authentication and [RevenueCat](https://www.revenuecat.com/docs/getting-started/installation/reactnative?utm_medium=sponsored&utm_source=youtube&utm_campaign=SimonGrimm) for In-App purchases.

Additional features:

- [Expo Router](https://docs.expo.dev/routing/introduction/) file-based navigation and API Routes
- [OpenAI API](https://platform.openai.com/) for GPT chat completions and image generation
- [Reanimated](https://docs.swmansion.com/react-native-reanimated/) 3 for animations
- [Gesture Handler](https://docs.swmansion.com/react-native-gesture-handler/) for gestures
- [Redash](https://github.com/wcandillon/react-native-redash) for animated text
- [Zeego](https://zeego.dev/start) for native menus
- [RN MMKV](https://github.com/mrousavy/react-native-mmkv) for efficient key/value storage
- [Expo SQLite](https://docs.expo.dev/versions/latest/sdk/sqlite-next/) for storing chats and messages
- [Bottom Sheet](https://ui.gorhom.dev/components/bottom-sheet/) for bottom sheet component
- [FlashList](https://shopify.github.io/flash-list/) for efficient list rendering
- [React Native OpenAI](https://github.com/candlefinance/react-native-openai) for streaming
- [Image Zoom](https://github.com/likashefqet/react-native-image-zoom) for image zoom component
- [Shimmer Placeholder](https://github.com/tomzaku/react-native-shimmer-placeholder) for loading placeholders

## Screenshots

<div style="display: flex; flex-direction: 'row';">
<img src="./screenshots/1.png" width=30%>
<img src="./screenshots/2.png" width=30%>
<img src="./screenshots/3.png" width=30%>
<img src="./screenshots/4.png" width=30%>
<img src="./screenshots/5.png" width=30%>
<img src="./screenshots/6.png" width=30%>
<img src="./screenshots/7.png" width=30%>
<img src="./screenshots/8.png" width=30%>
<img src="./screenshots/9.png" width=30%>
<img src="./screenshots/10.png" width=30%>
<img src="./screenshots/11.png" width=30%>
<img src="./screenshots/12.png" width=30%>
<img src="./screenshots/13.png" width=30%>
<img src="./screenshots/14.png" width=30%>
</div>

## Demo

<div style="display: flex; flex-direction: 'row';">
<img src="./screenshots/intro.gif" width=30%>
<img src="./screenshots/chat.gif" width=30%>
<img src="./screenshots/context.gif" width=30%>
<img src="./screenshots/sqlite.gif" width=30%>
<img src="./screenshots/purchase.gif" width=30%>
<img src="./screenshots/explore.gif" width=30%>

<img src="./screenshots/dalle.gif" width=30%>
<img src="./screenshots/imagecontext.gif" width=30%>
<img src="./screenshots/imagezoom.gif" width=30%>


</div>

## 🚀 More

**Take a shortcut from web developer to mobile development fluency with guided learning**

Enjoyed this project? Learn to use React Native to build production-ready, native mobile apps for both iOS and Android based on your existing web development skills.

<a href="https://galaxies.dev"><img src="banner.png" height="auto" width="100%"></a>
